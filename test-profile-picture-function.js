/**
 * Test script for doctor profile picture URL function
 * This script helps debug the production issue
 */

const { findBlobByPrefix } = require('./src/utils/blobUtils');

async function testProfilePictureFunction() {
  console.log('Testing doctor profile picture function...');
  
  // Test environment variables
  console.log('Environment Variables Check:');
  console.log('AZURE_STORAGE_ACCOUNT_NAME:', process.env.AZURE_STORAGE_ACCOUNT_NAME ? 'SET' : 'NOT SET');
  console.log('AzureWebJobsStorage:', process.env.AzureWebJobsStorage ? 'SET' : 'NOT SET');
  
  if (!process.env.AZURE_STORAGE_ACCOUNT_NAME) {
    console.error('❌ AZURE_STORAGE_ACCOUNT_NAME is not set');
    return;
  }
  
  if (!process.env.AzureWebJobsStorage) {
    console.error('❌ AzureWebJobsStorage is not set');
    return;
  }
  
  // Test blob operations
  const containerName = 'profile-pictures';
  const testDoctorId = 'test-doctor-123';
  const prefix = `doctor/${testDoctorId}/`;
  
  try {
    console.log(`\nTesting findBlobByPrefix with container: ${containerName}, prefix: ${prefix}`);
    const blobName = await findBlobByPrefix(containerName, prefix);
    
    if (blobName) {
      console.log('✅ Found blob:', blobName);
      const blobUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/${containerName}/${blobName}`;
      console.log('✅ Generated URL:', blobUrl);
    } else {
      console.log('ℹ️ No blob found for prefix (this is expected if no profile picture exists)');
    }
    
    console.log('✅ Function test completed successfully');
    
  } catch (error) {
    console.error('❌ Error during test:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testProfilePictureFunction().catch(console.error);
