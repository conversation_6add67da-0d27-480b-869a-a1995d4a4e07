const cosmosDbContext = require('../cosmosDbContext/comosdb-context')
const logging = require('../common/logging')
const physicalActivityLookupService = require('../services/physical-activity-lookup-service')
const DateFilterUtils = require('../utils/date-filter-utils')
const { DashboardFilter } = require('../common/constant')
const patientLifeStyleContainer = 'PatientLifeStyles'

class PatientLifestylePhysicalActivityRepository {
  // Get physical activity data from PatientLifeStyles container with date filtering
  async getPhysicalActivityData(patientId, dateFilter, customDateRange = null) {
    try {
      let query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns'`

      // Apply date filtering using the utility
      if (dateFilter && dateFilter !== DashboardFilter.ALL) {
        const dateRange = DateFilterUtils.getDateRange(
          dateFilter,
          customDateRange,
        )
        if (dateRange.startDate && dateRange.endDate) {
          query += ` AND c.created_on >= '${dateRange.startDate}T00:00:00.000Z' AND c.created_on <= '${dateRange.endDate}T23:59:59.999Z'`
        }
      }

      query += ` ORDER BY c.created_on ASC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      return results || []
    } catch (error) {
      logging.logError(
        `Failed to get physical activity data for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to fetch physical activity data')
    }
  }

  async getLatestLifestyleRecord(patientId) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns' ORDER BY c.updated_on DESC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      return results && results.length > 0 ? results[0] : null
    } catch (error) {
      logging.logError(
        `Failed to get latest lifestyle record for patient ${patientId}:`,
        error,
      )
      return null
    }
  }

  // Get ALL lifestyle records for a patient (not filtered by date)
  async getAllLifestyleRecords(patientId) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns' ORDER BY c.updated_on ASC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )

      logging.logInfo(
        `Retrieved ${
          results?.length || 0
        } total lifestyle records for patient ${patientId}`,
      )
      return results || []
    } catch (error) {
      logging.logError(
        `Failed to get all lifestyle records for patient ${patientId}:`,
        error,
      )
      return []
    }
  }

  // Generate smart projected activities based on multiple records and date ranges
  async generateSmartProjectedActivities(
    allRecords,
    startDate,
    endDate,
    patientWeight,
  ) {
    try {
      if (!allRecords || allRecords.length === 0) {
        return []
      }

      const projectedActivities = []

      // For each day in the requested range, find the applicable record
      const dateRange = this.generateDateRange(startDate, endDate)

      for (const date of dateRange) {
        // Find the most recent record that was updated on or before this date
        const applicableRecord = this.findApplicableRecordForDate(
          allRecords,
          date,
        )

        if (applicableRecord) {
          // Extract activities from the applicable record
          const activities = this.extractActivitiesFromRecord(applicableRecord)

          for (const activity of activities) {
            if (!activity.frequency) continue

            // Check if this activity should occur on this date based on frequency
            if (
              this.shouldActivityOccurOnDate(
                activity.frequency,
                date,
                applicableRecord.updated_on.split('T')[0],
              )
            ) {
              // Get MET value and calculate calories
              const metValue = await physicalActivityLookupService.getMetValue(
                activity.activity,
                activity.intensity,
                activity.activity_type,
              )

              const duration = parseFloat(activity.duration)
              const metMinutes = metValue ? duration * metValue : 0
              const caloriesBurned = metValue
                ? metValue * patientWeight * (duration / 60)
                : 0

              // Add day name
              const dayName = new Date(date).toLocaleDateString('en-US', {
                weekday: 'short',
              })

              projectedActivities.push({
                id: `${applicableRecord.id}_${activity.activity_type}_${date}`,
                patientId: applicableRecord.patientId,
                date: date,
                dayName: dayName,
                activityType: activity.activity_type,
                activity: activity.activity,
                intensity: activity.intensity,
                duration: duration,
                frequency: activity.frequency,
                metValue: metValue || 0,
                metMinutes: metMinutes,
                caloriesBurned: Math.round(caloriesBurned * 100) / 100,
                doctorName:
                  applicableRecord.doctorName ||
                  applicableRecord.doctor?.name ||
                  '',
                doctorDesignation:
                  applicableRecord.doctorDesignation ||
                  applicableRecord.doctor?.designation ||
                  '',
                created_on: applicableRecord.created_on,
                updated_on: applicableRecord.updated_on,
                isProjected: true,
                recordId: applicableRecord.id,
              })
            }
          }
        }
      }

      // Remove duplicates and sort by date
      const uniqueActivities =
        this.removeDuplicateActivities(projectedActivities)
      uniqueActivities.sort((a, b) => new Date(a.date) - new Date(b.date))

      logging.logInfo(
        `Generated ${uniqueActivities.length} smart projected activities from ${startDate} to ${endDate}`,
      )
      return uniqueActivities
    } catch (error) {
      logging.logError('Failed to generate smart projected activities:', error)
      return []
    }
  }

  // Find the most recent record that applies to a specific date
  findApplicableRecordForDate(allRecords, targetDate) {
    // Sort records by updated_on date (ascending)
    const sortedRecords = allRecords.sort(
      (a, b) => new Date(a.updated_on) - new Date(b.updated_on),
    )

    let applicableRecord = null

    for (const record of sortedRecords) {
      const recordDate = record.updated_on.split('T')[0]

      // If this record was updated on or before the target date, it could be applicable
      if (recordDate <= targetDate) {
        applicableRecord = record
      } else {
        // If we've reached a record that was updated after the target date, stop
        break
      }
    }

    return applicableRecord
  }

  // Check if an activity should occur on a specific date based on frequency
  shouldActivityOccurOnDate(frequency, targetDate, recordStartDate) {
    const targetDay = new Date(targetDate).getDay() // 0 = Sunday, 1 = Monday, etc.
    const recordStart = new Date(recordStartDate)
    const target = new Date(targetDate)

    // Only project activities from the record start date onwards
    if (target < recordStart) {
      return false
    }

    switch (frequency.toLowerCase()) {
      case 'daily':
        return true

      case 'three times a week':
        return targetDay === 1 || targetDay === 3 || targetDay === 5 // Mon, Wed, Fri

      case 'four times a week':
        return (
          targetDay === 1 ||
          targetDay === 3 ||
          targetDay === 5 ||
          targetDay === 6
        ) // Mon, Wed, Fri, Sat

      case 'five times a week':
        return targetDay >= 1 && targetDay <= 5 // Mon to Fri

      case 'six times a week':
        return targetDay !== 0 // All days except Sunday

      case 'weekly':
        return targetDay === 1 // Only Mondays

      default:
        return false
    }
  }

  // Extract and process physical activity records from lifestyle data
  async processPhysicalActivityRecords(lifestyleRecords, patientWeight) {
    const processedRecords = []

    for (const record of lifestyleRecords) {
      if (!record.questions || !Array.isArray(record.questions)) continue

      for (const question of record.questions) {
        if (!question.fields) continue

        // Handle different field structures
        let activities = []

        if (Array.isArray(question.fields)) {
          // Handle array of arrays structure
          for (const fieldGroup of question.fields) {
            if (Array.isArray(fieldGroup)) {
              activities = activities.concat(fieldGroup)
            } else if (fieldGroup && typeof fieldGroup === 'object') {
              // Handle table structure with numbered keys
              const tableData = Object.keys(fieldGroup)
                .filter((key) => !isNaN(key))
                .map((key) => fieldGroup[key])
                .filter(
                  (item) =>
                    item &&
                    item.activity_type &&
                    item.activity &&
                    item.duration,
                )

              activities = activities.concat(tableData)
            }
          }
        }

        // Process each activity
        for (const activity of activities) {
          if (
            !activity.activity_type ||
            !activity.activity ||
            !activity.duration ||
            !activity.intensity
          ) {
            continue
          }

          // Skip empty activities
          if (
            !activity.activity.trim() ||
            activity.duration === '0' ||
            activity.duration === ''
          ) {
            continue
          }

          const duration = parseFloat(activity.duration)
          if (isNaN(duration) || duration <= 0) continue

          // Get MET value from lookup service
          const metValue = await physicalActivityLookupService.getMetValue(
            activity.activity,
            activity.intensity,
            activity.activity_type,
          )

          // Calculate MET Minutes and Calories
          const metMinutes = metValue ? duration * metValue : 0
          const caloriesBurned = metValue
            ? metValue * patientWeight * (duration / 60)
            : 0

          processedRecords.push({
            id: record.id,
            patientId: record.patientId,
            date: record.created_on.split('T')[0], // Extract date part
            activityType: activity.activity_type,
            activity: activity.activity,
            intensity: activity.intensity,
            duration: duration,
            frequency: activity.frequency || '',
            metValue: metValue || 0,
            metMinutes: metMinutes,
            caloriesBurned: Math.round(caloriesBurned * 100) / 100, // Round to 2 decimal places
            doctorName: record.doctorName || record.doctor?.name || '',
            doctorDesignation:
              record.doctorDesignation || record.doctor?.designation || '',
            created_on: record.created_on,
            updated_on: record.updated_on,
          })
        }
      }
    }

    return processedRecords
  }

  async generateFrequencyBasedActivities(
    latestRecord,
    startDate,
    endDate,
    patientWeight,
  ) {
    const projectedActivities = []

    if (!latestRecord || !latestRecord.questions) {
      return projectedActivities
    }

    const activities = this.extractActivitiesFromRecord(latestRecord)

    const dateRange = this.generateDateRange(startDate, endDate)

    for (const activity of activities) {
      if (!activity.frequency) continue

      const applicableDates = this.getApplicableDatesForFrequency(
        activity.frequency,
        dateRange,
      )

      for (const date of applicableDates) {
        const metValue = await physicalActivityLookupService.getMetValue(
          activity.activity,
          activity.intensity,
          activity.activity_type,
        )
        console.log(metValue, 'metValue')

        const duration = parseFloat(activity.duration)
        const metMinutes = metValue ? duration * metValue : 0
        const caloriesBurned = metValue
          ? metValue * patientWeight * (duration / 60)
          : 0

        const dayName = new Date(date).toLocaleDateString('en-US', {
          weekday: 'short',
        })

        projectedActivities.push({
          id: `${latestRecord.id}_${activity.activity_type}_${date}`,
          patientId: latestRecord.patientId,
          date: date,
          dayName: dayName,
          activityType: activity.activity_type,
          activity: activity.activity,
          intensity: activity.intensity,
          duration: duration,
          frequency: activity.frequency,
          metValue: metValue || 0,
          metMinutes: metMinutes,
          caloriesBurned: Math.round(caloriesBurned * 100) / 100,
          doctorName:
            latestRecord.doctorName || latestRecord.doctor?.name || '',
          doctorDesignation:
            latestRecord.doctorDesignation ||
            latestRecord.doctor?.designation ||
            '',
          created_on: latestRecord.created_on,
          updated_on: latestRecord.updated_on,
          isProjected: true,
        })
      }
    }

    return projectedActivities
  }

  extractActivitiesFromRecord(record) {
    const activities = []

    if (!record.questions || !Array.isArray(record.questions)) {
      return activities
    }

    for (const question of record.questions) {
      if (!question.fields || !Array.isArray(question.fields)) continue

      for (const field of question.fields) {
        if (!field || typeof field !== 'object') continue

        // Check if field has a value property that contains activities
        if (field.value && Array.isArray(field.value)) {
          // This handles the table structure where activities are in field.value array
          const validActivities = field.value.filter(this.isValidActivity)
          activities.push(...validActivities)
        } else if (Array.isArray(field)) {
          // This handles direct array of activities
          const validActivities = field.filter(this.isValidActivity)
          activities.push(...validActivities)
        } else if (field.type === 'table' && field.value) {
          // Explicit handling for table type fields
          if (Array.isArray(field.value)) {
            const validActivities = field.value.filter(this.isValidActivity)
            activities.push(...validActivities)
          }
        }
      }
    }

    return activities
  }

  isValidActivity(activity) {
    return (
      activity &&
      activity.activity_type &&
      activity.activity &&
      activity.duration &&
      activity.intensity &&
      activity.activity.trim() &&
      activity.duration !== '0' &&
      activity.duration !== '' &&
      !isNaN(parseFloat(activity.duration)) &&
      parseFloat(activity.duration) > 0
    )
  }

  generateDateRange(startDate, endDate) {
    const dates = []
    const start = new Date(startDate)
    const end = new Date(endDate)

    for (
      let date = new Date(start);
      date <= end;
      date.setDate(date.getDate() + 1)
    ) {
      dates.push(date.toISOString().split('T')[0])
    }

    return dates
  }

  getApplicableDatesForFrequency(frequency, dateRange) {
    switch (frequency.toLowerCase()) {
      case 'daily':
        return dateRange

      case 'three times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay() // 0 = Sunday, 1 = Monday, etc.
          return dayOfWeek === 1 || dayOfWeek === 2 || dayOfWeek === 3 // Mon, Tue, Wed
        })

      case 'four times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return (
            dayOfWeek === 1 ||
            dayOfWeek === 2 ||
            dayOfWeek === 3 ||
            dayOfWeek === 4
          ) // Mon, Tue, Wed, Thu
        })

      case 'five times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return dayOfWeek >= 1 && dayOfWeek <= 5 // Mon to Fri
        })

      case 'six times a week':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return dayOfWeek !== 0 // All days except Sunday
        })

      case 'weekly':
        return dateRange.filter((date) => {
          const dayOfWeek = new Date(date).getDay()
          return dayOfWeek === 1 // Only Mondays
        })

      default:
        return [] // Unknown frequency
    }
  }

  async getLifestyleRecordsInRange(patientId, startDate, endDate) {
    try {
      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns' AND c.updated_on >= '${startDate}T00:00:00.000Z' AND c.updated_on <= '${endDate}T23:59:59.999Z' ORDER BY c.updated_on DESC`

      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )

      return results || []
    } catch (error) {
      logging.logError(
        `Failed to get lifestyle records in range for patient ${patientId}:`,
        error,
      )
      return []
    }
  }

  async getAllLifestyleRecordsForDateRange(patientId, startDate, endDate) {
    try {
      const start = new Date(startDate)
      const end = new Date(endDate)

      const diffMs = end - start

      const diffDays = Math.ceil(diffMs / (1000 * 60 * 60 * 24))

      const bufferDate = new Date(end)
      bufferDate.setDate(bufferDate.getDate() - diffDays)

      const bufferDateStr = bufferDate.toISOString().split('T')[0]
      console.log(bufferDateStr, 'bufferDateStr')

      const query = `SELECT * FROM c WHERE c.patientId = '${patientId}' AND c.source = 'physical_activity_practice_exercise_patterns' AND c.updated_on >= '${bufferDateStr}T00:00:00.000Z' ORDER BY c.updated_on ASC`
      console.log(query, 'query')
      const results = await cosmosDbContext.queryItems(
        query,
        patientLifeStyleContainer,
      )
      console.log(results, 'results')

      const relevantRecords = results.filter((record) => {
        const recordDate = record.updated_on.split('T')[0]
        return recordDate <= endDate
      })

      logging.logInfo(
        `Found ${relevantRecords.length} relevant lifestyle records for patient ${patientId} in date range ${startDate} to ${endDate}`,
      )

      return relevantRecords || []
    } catch (error) {
      logging.logError(
        `Failed to get all lifestyle records for date range for patient ${patientId}:`,
        error,
      )
      return []
    }
  }

  async getProcessedPhysicalActivityData(
    patientId,
    dateFilter,
    customDateRange = null,
    patientWeight,
  ) {
    try {
      let startDate, endDate

      if (dateFilter && dateFilter !== DashboardFilter.ALL) {
        const dateRange = DateFilterUtils.getDateRange(
          dateFilter,
          customDateRange,
        )
        startDate = dateRange.startDate
        endDate = dateRange.endDate
      } else {
        endDate = new Date().toISOString().split('T')[0]
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split('T')[0]
      }

      // Get ALL lifestyle records for this patient (not filtered by date)
      const allRecords = await this.getAllLifestyleRecords(patientId)

      if (!allRecords || allRecords.length === 0) {
        logging.logInfo(`No lifestyle records found for patient ${patientId}`)
        return []
      }

      logging.logInfo(
        `Found ${allRecords.length} total lifestyle records for patient ${patientId}`,
      )

      // Generate smart projected activities for the requested date range
      const projectedActivities = await this.generateSmartProjectedActivities(
        allRecords,
        startDate,
        endDate,
        patientWeight,
      )

      logging.logInfo(
        `Generated ${projectedActivities.length} projected activities for patient ${patientId} from ${startDate} to ${endDate}`,
      )

      return projectedActivities
    } catch (error) {
      logging.logError(
        `Failed to get processed physical activity data for patient ${patientId}:`,
        error,
      )
      throw new Error('Failed to process physical activity data')
    }
  }

  // Helper to get the next record date for range calculation
  getNextRecordDate(currentRecord, allRecords) {
    const currentDate = new Date(currentRecord.updated_on)
    const nextRecord = allRecords
      .filter((r) => new Date(r.updated_on) > currentDate)
      .sort((a, b) => new Date(a.updated_on) - new Date(b.updated_on))[0]

    return nextRecord ? nextRecord.updated_on.split('T')[0] : null
  }

  // Remove duplicate activities (same date, activity type, activity)
  removeDuplicateActivities(activities) {
    const seen = new Set()
    return activities.filter((activity) => {
      const key = `${activity.date}_${activity.activityType}_${activity.activity}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }
}

module.exports = new PatientLifestylePhysicalActivityRepository()
