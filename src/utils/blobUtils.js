const { BlobServiceClient } = require('@azure/storage-blob')

const connectionString = process.env.AzureWebJobsStorage || ''
const blobServiceClient =
  BlobServiceClient.fromConnectionString(connectionString)

/**
 * Get a container client.
 */
const getContainerClient = (containerName) =>
  blobServiceClient.getContainerClient(containerName)

/**
 * Find a blob by prefix.
 */
const findBlobByPrefix = async (containerName, prefix) => {
  try {
    if (!containerName || !prefix) {
      throw new Error('Container name and prefix are required')
    }

    const containerClient = getContainerClient(containerName)

    // Check if container exists
    const containerExists = await containerClient.exists()
    if (!containerExists) {
      console.log(`Container ${containerName} does not exist`)
      return null
    }

    const blobs = containerClient.listBlobsFlat({ prefix })
    for await (const blob of blobs) {
      return blob.name
    }
    return null
  } catch (error) {
    console.error(
      `Error finding blob with prefix ${prefix} in container ${containerName}:`,
      error.message,
    )
    throw error
  }
}

/**
 * Delete blobs by prefix.
 */
const deleteBlobsByPrefix = async (containerName, prefix) => {
  try {
    if (!containerName || !prefix) {
      throw new Error('Container name and prefix are required')
    }

    const containerClient = getContainerClient(containerName)

    // Check if container exists
    const containerExists = await containerClient.exists()
    if (!containerExists) {
      console.log(
        `Container ${containerName} does not exist, nothing to delete`,
      )
      return
    }

    const blobs = containerClient.listBlobsFlat({ prefix })
    let deletedCount = 0

    for await (const blob of blobs) {
      const blockBlobClient = containerClient.getBlockBlobClient(blob.name)
      const deleteResult = await blockBlobClient.deleteIfExists()
      if (deleteResult.succeeded) {
        deletedCount++
      }
    }

    console.log(
      `Deleted ${deletedCount} blobs with prefix ${prefix} from container ${containerName}`,
    )
  } catch (error) {
    console.error(
      `Error deleting blobs with prefix ${prefix} from container ${containerName}:`,
      error.message,
    )
    throw error
  }
}

/**
 * Upload a blob.
 */
const uploadBlob = async (containerName, blobName, buffer) => {
  const containerClient = getContainerClient(containerName)
  await containerClient.createIfNotExists({ access: 'blob' })
  const blockBlobClient = containerClient.getBlockBlobClient(blobName)
  await blockBlobClient.uploadData(buffer)
  return blockBlobClient.url
}

module.exports = {
  getContainerClient,
  findBlobByPrefix,
  deleteBlobsByPrefix,
  uploadBlob,
}
